import { envRequest } from '@/request';

// ===== 类型定义 =====

/**
 * 通用 API 响应接口
 */
export type ApiResponse<T = unknown> = {
  code: number;
  data: T;
  msg?: string;
  total?: number;
};

/**
 * 分页查询参数接口
 */
interface PageQueryParams {
  aescs?: string[];
  descs?: string[];
  condition: Record<string, unknown>;
  currentPage: number;
  pageSize: number;
}

/**
 * 溯源记录数据接口
 */
export interface TraceRecord {
  id: string;
  equipNm: string; // 设备名称
  supplyUnit: string; // 供应单位
  description: string; // 监测对象
  indexNm: string; // 监测指标名称
  traceTime: string; // 溯源时间
  traceResult: string; // 溯源结果
  traceStatus: string; // 溯源状态：0-待处理，1-已处理
  traceStatusText?: string; // 溯源状态文本
  createTime: string; // 创建时间
  updateTime?: string; // 更新时间
  serialNo?: number; // 序号
}

/**
 * 溯源处理参数接口
 */
export interface TraceHandleParams {
  id: string;
  handlePsn: string; // 处理人
  handleTime: string; // 处理时间
  handleContent: string; // 处理内容
  handleResult: string; // 处理结果
}

/**
 * 溯源 API 接口定义
 */
interface TraceApiInterface {
  /** 查询溯源记录列表 */
  queryTraceRecordList: (data: PageQueryParams) => Promise<ApiResponse<TraceRecord[]>>;
  /** 处理溯源记录 */
  handleTraceRecord: (data: TraceHandleParams) => Promise<ApiResponse<unknown>>;
  /** 查询溯源记录详情 */
  queryTraceRecordDetail: (id: string) => Promise<ApiResponse<TraceRecord>>;
  /** 生成溯源报告 */
  generateTraceReport: (data: Record<string, unknown>) => Promise<ApiResponse<unknown>>;
}

/**
 * 溯源模块 API
 */
const traceApi: TraceApiInterface = {
  /** 查询溯源记录列表 */
  queryTraceRecordList: (data: PageQueryParams) => {
    return envRequest.post('/trace/page', { data });
  },

  /** 处理溯源记录 */
  handleTraceRecord: (data: TraceHandleParams) => {
    return envRequest.post('/trace/handle', { data });
  },

  /** 查询溯源记录详情 */
  queryTraceRecordDetail: (id: string) => {
    return envRequest.get(`/trace/detail/${id}`);
  },

  /** 生成溯源报告 */
  generateTraceReport: (data: Record<string, unknown>) => {
    return envRequest.post('/trace/generateReport', { data });
  },
};

export default traceApi;
