import React, { useState, useRef } from 'react';
import { YTHList, YTHLocalization } from 'yth-ui';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import { Modal, Button, message, Space } from 'antd';
import traceApi from '@/service/traceApi';
import type { TraceRecord, ApiResponse } from '@/service/traceApi';
import formApi from '@/service/formApi';
import locales from '@/locales';
import commonstyle from '../envAlarm/common.module.less';
import TraceabilityModal from './TraceabilityModal';

type filterType = {
  equipNm?: string;
  supplyUnit?: string;
  description?: string;
  indexNm?: string;
  traceStatus?: { code: string; text: string }[];
  traceTime?: string;
};

type filterTypeData = {
  equipNm?: string;
  supplyUnit?: string;
  description?: string;
  indexNm?: string;
  traceStatus?: string;
  traceTime?: string;
};

/**
 * @description 溯源管理页面
 * @returns
 */
const TraceabilityList: React.FC = () => {
  const ref: React.MutableRefObject<ActionType> = useRef<ActionType>();
  const aa: ActionType = YTHList.createAction();
  const [modalType, setModalType] = useState<'handle' | 'view'>('view'); // view handle 查看还是处理
  const [dataObj, setDataObj] = useState<Record<string, unknown>>({});
  const [editMenuVisiable, setEditMenuVisiable] = useState<boolean>(false); // 标记弹窗是否显示

  /**
   * 处理筛选条件
   * @param filter 筛选条件
   * @returns 处理后的筛选条件
   */
  const handleFilter = (filter: filterType): filterTypeData => {
    const result: filterTypeData = {};

    if (filter.equipNm) {
      result.equipNm = filter.equipNm;
    }
    if (filter.supplyUnit) {
      result.supplyUnit = filter.supplyUnit;
    }
    if (filter.description) {
      result.description = filter.description;
    }
    if (filter.indexNm) {
      result.indexNm = filter.indexNm;
    }
    if (filter.traceStatus && filter.traceStatus.length > 0) {
      result.traceStatus = filter.traceStatus[0].code;
    }
    if (filter.traceTime) {
      result.traceTime = filter.traceTime;
    }

    return result;
  };

  /**
   * 关闭弹窗
   */
  const closeModal = (): void => {
    setEditMenuVisiable(false);
    setDataObj({});
    // 刷新列表
    if (ref.current) {
      ref.current.reload();
    }
  };

  /**
   * 表格列配置
   */
  const columns: IYTHColumnProps[] = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      display: false,
    },
    {
      dataIndex: 'equipNm',
      title: '设备名称',
      width: 150,
      query: true,
      display: true,
      componentName: 'Input',
      componentProps: {
        placeholder: '请输入设备名称',
      },
    },
    {
      dataIndex: 'supplyUnit',
      title: '供应单位',
      width: 150,
      query: true,
      display: true,
      componentName: 'Input',
      componentProps: {
        placeholder: '请输入供应单位',
      },
    },
    {
      dataIndex: 'description',
      title: '监测对象',
      width: 120,
      query: true,
      display: true,
      componentName: 'Input',
      componentProps: {
        placeholder: '请输入监测对象',
      },
    },
    {
      dataIndex: 'indexNm',
      title: '监测指标',
      width: 120,
      query: true,
      display: true,
      componentName: 'Input',
      componentProps: {
        placeholder: '请输入监测指标',
      },
    },
    {
      dataIndex: 'traceTime',
      title: '溯源时间',
      width: 160,
      query: true,
      display: true,
      componentName: 'DatePicker',
      componentProps: {
        precision: 'second',
        formatter: 'YYYY-MM-DD HH:mm:ss',
        placeholder: '请选择溯源时间',
      },
    },
    {
      dataIndex: 'traceResult',
      title: '溯源结果',
      width: 200,
      ellipsis: true,
    },
    {
      dataIndex: 'traceStatusText',
      title: '处理状态',
      width: 100,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        request: async () => {
          const { list } = await formApi.getDictionary({
            condition: {
              fatherCode: 'A03A01', // 处理状态字典
            },
            currentPage: 0,
            pageSize: 0,
          });
          return list.map((item) => ({
            text: item.text,
            code: item.code,
          }));
        },
        multiple: false,
        placeholder: '请选择处理状态',
      },
      render: (value: string, record: TraceRecord) => {
        if (record.traceStatus === '0') {
          return <span style={{ color: '#ff4d4f' }}>待处理</span>;
        } else if (record.traceStatus === '1') {
          return <span style={{ color: '#52c41a' }}>已处理</span>;
        }
        return value || '-';
      },
    },
    {
      dataIndex: 'createTime',
      title: '创建时间',
      width: 160,
    },
  ];

  return (
    <div className={commonstyle['gas-leak-monitor-template-container']}>
      <YTHList
        defaultQuery={{}}
        code="traceabilityList"
        action={aa}
        searchMemory
        actionRef={ref}
        showRowSelection={false}
        operation={[]}
        listKey="id"
        extraOperation={[]}
        request={async (filter, pagination) => {
          const resData: ApiResponse<TraceRecord[]> = await traceApi.queryTraceRecordList({
            aescs: [],
            descs: [],
            condition: handleFilter(filter),
            currentPage: pagination.current,
            pageSize: pagination.pageSize,
          });
          if (resData.code && resData.code === 200) {
            resData.data.forEach((item, index) => {
              resData.data[index].serialNo =
                (pagination.current - 1) * pagination.pageSize + index + 1;
            });
            return {
              data: resData.data,
              total: resData.total,
              success: true,
            };
          }
          message.error(resData.msg || '查询失败');
          return {
            data: [],
            total: 0,
            success: false,
          };
        }}
        rowOperationWidth={150}
        rowOperation={(row) => {
          return [
            {
              element: (
                <div className={commonstyle['gas-leak-monitor-row-operator']}>
                  <Space size="middle">
                    <Button
                      type="link"
                      size="small"
                      className={commonstyle['p-btn-detail']}
                      onClick={() => {
                        setModalType('view');
                        setDataObj(row);
                        setEditMenuVisiable(true);
                      }}
                    >
                      查看
                    </Button>
                    {row.traceStatus && row.traceStatus === '0' && (
                      <Button
                        type="link"
                        size="small"
                        className={commonstyle['p-btn-detail']}
                        onClick={() => {
                          setModalType('handle');
                          setDataObj(row);
                          setEditMenuVisiable(true);
                        }}
                      >
                        处理
                      </Button>
                    )}
                    {!row.traceStatus ||
                      (row.traceStatus !== '0' && (
                        <Button type="link" size="small" disabled>
                          处理
                        </Button>
                      ))}
                  </Space>
                </div>
              ),
            },
          ];
        }}
        columns={columns}
      />

      <Modal
        title={modalType === 'view' ? '溯源记录详情' : '溯源处理'}
        width="70%"
        footer={null}
        destroyOnClose
        onCancel={closeModal}
        maskClosable={false}
        visible={editMenuVisiable}
        key="traceability-modal"
      >
        <TraceabilityModal modalType={modalType} closeModal={closeModal} dataObj={dataObj} />
      </Modal>
    </div>
  );
};

export default YTHLocalization.withLocal(TraceabilityList, locales, YTHLocalization.getLanguage());
