/* 溯源管理页面样式 */
.traceability-container {
  width: 100%;
  height: 100%;
}

/* 溯源处理表单样式 */
.trace-handle-form {
  width: 100%;
  padding: 20px;

  .form-item {
    margin-bottom: 16px;
  }

  .form-buttons {
    width: 100%;
    margin-top: 20px;
    display: flex;
    flex-direction: row-reverse;

    .save-btn {
      margin-left: auto;
      margin-right: 10px;
      background-color: #007EBB;
    }

    .cancel-btn {
      margin-right: 20px;
    }
  }
}

/* 溯源详情查看样式 */
.trace-view-container {
  width: 100%;
  padding: 20px;

  .detail-item {
    display: flex;
    margin-bottom: 16px;
    align-items: center;

    .label {
      width: 120px;
      color: #626161;
      font-weight: 500;
    }

    .value {
      flex: 1;
      color: #333;
    }
  }

  .result-content {
    .label {
      width: 120px;
      color: #626161;
      font-weight: 500;
      vertical-align: top;
      margin-top: 8px;
    }

    .value {
      flex: 1;
      min-height: 80px;
      padding: 8px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      background-color: #fafafa;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}

/* 状态标签样式 */
.status-tag {
  &.pending {
    color: #ff4d4f;
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
  }

  &.completed {
    color: #52c41a;
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
  }
}

/* 表格行操作按钮样式 */
.row-operation {
  display: flex;
  gap: 8px;

  .btn-view {
    color: #0687c8;
    padding: 0;
    border: none;
    background: none;
    cursor: pointer;

    &:hover {
      color: #0574b0;
    }
  }

  .btn-handle {
    color: #52c41a;
    padding: 0;
    border: none;
    background: none;
    cursor: pointer;

    &:hover {
      color: #389e0d;
    }
  }

  .btn-disabled {
    color: #d9d9d9;
    padding: 0;
    border: none;
    background: none;
    cursor: not-allowed;
  }
}
